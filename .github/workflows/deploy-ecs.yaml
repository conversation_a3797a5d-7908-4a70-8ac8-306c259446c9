name: ECS Build & Deploy

on:
  workflow_call:
    inputs:
      service:
        type: string
        description: "Name of the service"
        required: true
      env:
        type: string
        required: true
      ref:
        type: string
        description: "Branch, tag, or commit ID to deploy"
        required: false
    secrets:
      AWS_REGION:
        required: true
      AWS_ACCOUNT_ID:
        required: true
      AWS_CLUSTER:
        required: true
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
        
jobs:
  build-push-deploy:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: ${{ secrets.AWS_REGION }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
      AWS_CLUSTER: ${{ secrets.AWS_CLUSTER }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Install yq and jq
        run: |
          sudo apt-get update && sudo apt-get install -y jq
          sudo wget https://github.com/mikefarah/yq/releases/download/v4.43.1/yq_linux_amd64 -O /usr/bin/yq
          sudo chmod +x /usr/bin/yq

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
  
      
      - name: make .env for buildtime env vars
        run: |
          SPECS_FILE="deploy/${{ inputs.env }}/${{ inputs.service }}/service-spec.yaml"
          yq '.service.environment | to_entries | .[] | .key + "=" + (.value | tostring)' deploy/${{ inputs.env }}/${{ inputs.service }}/service-spec.yaml > ${{ inputs.service }}/.env

      - name: Build and Push Docker Image
        id: build-push
        run: |
          if [ -z "${{ inputs.ref }}" ]; then
            IMAGE_TAG=$(echo "${GITHUB_SHA}" | cut -c1-7)
          else
            IMAGE_TAG=${{ inputs.ref }}
          fi
          IMAGE_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${{ inputs.service }}:${IMAGE_TAG}
          
          # Add build args for nginx
          if [ "${{ inputs.service }}" = "nginx" ]; then
            docker build --build-arg ENV=${{ inputs.env }} -t $IMAGE_URI ./${{ inputs.service }}
          else
            docker build -t $IMAGE_URI ./${{ inputs.service }}
          fi
          
          docker push $IMAGE_URI
          echo "image_uri=$IMAGE_URI" >> $GITHUB_ENV

      - name: Deploy Service
        run: |
          SPECS=$(yq e '.' "deploy/${{ inputs.env }}/${{ inputs.service }}/service-spec.yaml" -o=json)
          
          SERVICE_TD=$(aws ecs describe-services \
            --cluster ${{ env.AWS_CLUSTER }} \
            --services ${{ inputs.service }} \
            --query 'services[0].taskDefinition' \
            --output text | \
            xargs aws ecs describe-task-definition \
            --task-definition \
            --query 'taskDefinition' \
            --output json)

          echo $SERVICE_TD | jq --argjson specs "$SPECS" '
            .containerDefinitions[0].image = "${{ env.image_uri }}" |
            if $specs.service.cpu then .containerDefinitions[0].cpu = ($specs.service.cpu|tonumber) else . end |
            if $specs.service.memory then .containerDefinitions[0].memory = ($specs.service.memory|tonumber) else . end |
            if $specs.service.environment then .containerDefinitions[0].environment = ($specs.service.environment | to_entries | map({name: .key, value: (.value|tostring)})) else . end |
            if $specs.service.secrets then .containerDefinitions[0].secrets = $specs.service.secrets else . end |
            del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)
          ' > service-task-definition.json

          SERVICE_TD_ARN=$(aws ecs register-task-definition \
            --cli-input-json file://service-task-definition.json \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)

          aws ecs update-service \
            --cluster ${{ env.AWS_CLUSTER }} \
            --service ${{ inputs.service }} \
            --task-definition $SERVICE_TD_ARN \
            --desired-count $(echo "$SPECS" | jq -r '.service.desired_tasks // 1') \
            --force-new-deployment

      - name: Deploy Workers
        if: ${{ success() }}
        run: |
          SPECS=$(yq e '.' "deploy/${{ inputs.env }}/${{ inputs.service }}/service-spec.yaml" -o=json)
          IMAGE_URI="${{ env.image_uri }}"

          WORKERS_LENGTH=$(echo "$SPECS" | jq '.workers | length')

          if [ "$WORKERS_LENGTH" -eq 0 ]; then
            echo "No workers defined. Skipping."
            exit 0
          fi

          for i in $(seq 0 $((WORKERS_LENGTH - 1))); do
            ENABLED=$(echo "$SPECS" | jq -r ".workers[$i].enabled")
            if [ "$ENABLED" != "true" ]; then
              echo "Skipping worker at index $i (disabled)"
              continue
            fi

            WORKER_NAME=$(echo "$SPECS" | jq -r ".workers[$i].name")
            echo "Deploying worker: $WORKER_NAME"

            # Generate the task definition
            cat service-task-definition.json | jq \
              --argjson worker "$(echo "$SPECS" | jq ".workers[$i]")" \
              --arg image "$IMAGE_URI" \
              --arg name "$WORKER_NAME" '
              .containerDefinitions[0].name = $name |
              .containerDefinitions[0].image = $image |
              if $worker.cpu then .containerDefinitions[0].cpu = ($worker.cpu | tonumber) else . end |
              if $worker.memory then .containerDefinitions[0].memory = ($worker.memory | tonumber) else . end |
              if $worker.environment then .containerDefinitions[0].environment = ($worker.environment | to_entries | map({name: .key, value: (.value|tostring)})) else . end |
              if $worker.secrets then .containerDefinitions[0].secrets = $worker.secrets else . end |
              if $worker.command then .containerDefinitions[0].command = $worker.command else . end
            ' > "worker-task-definition-${WORKER_NAME}.json"

            # Register the new task definition
            WORKER_TD_ARN=$(aws ecs register-task-definition \
              --cli-input-json file://worker-task-definition-${WORKER_NAME}.json \
              --query 'taskDefinition.taskDefinitionArn' \
              --output text)

            # Update the ECS service
            DESIRED_COUNT=$(echo "$SPECS" | jq -r ".workers[$i].desired_tasks // 1")
            aws ecs update-service \
              --cluster ${{ env.AWS_CLUSTER }} \
              --service "$WORKER_NAME" \
              --task-definition "$WORKER_TD_ARN" \
              --desired-count "$DESIRED_COUNT" \
              --force-new-deployment
          done

      - name: Wait for deployments to complete
        run: |
          SPECS=$(yq e '.' "deploy/${{ inputs.env }}/${{ inputs.service }}/service-spec.yaml" -o=json)

          # Wait for main service
          echo "Waiting for service ${{ inputs.service }} to become stable..."
          aws ecs wait services-stable \
            --cluster ${{ env.AWS_CLUSTER }} \
            --services ${{ inputs.service }}

          # Wait for workers (if any are defined)
          WORKERS_LENGTH=$(echo "$SPECS" | jq '.workers | length')

          if [ "$WORKERS_LENGTH" -gt 0 ]; then
            echo "Waiting for workers to become stable..."
            WORKER_SERVICES=()

            for i in $(seq 0 $((WORKERS_LENGTH - 1))); do
              ENABLED=$(echo "$SPECS" | jq -r ".workers[$i].enabled")
              if [ "$ENABLED" = "true" ]; then
                WORKER_NAME=$(echo "$SPECS" | jq -r ".workers[$i].name")
                WORKER_SERVICES+=("$WORKER_NAME")
                echo "Will wait for worker: $WORKER_NAME"
              fi
            done

            # Wait for all workers in parallel
            if [ ${#WORKER_SERVICES[@]} -gt 0 ]; then
              for worker in "${WORKER_SERVICES[@]}"; do
                echo "Waiting for worker service $worker to become stable..."
                aws ecs wait services-stable \
                  --cluster ${{ env.AWS_CLUSTER }} \
                  --services "$worker" &
              done

              # Wait for all background jobs to complete
              wait
              echo "All worker services are now stable!"
            fi
          else
            echo "No workers defined. Skipping worker wait."
          fi

          echo "All deployments completed successfully!"
          if [ -f "worker-task-definition.json" ]; then
            WORKER_NAME=$(yq e '.worker.name' "deploy/${{ inputs.env }}/${{ inputs.service }}/service-spec.yaml")
            echo "Waiting for worker service $WORKER_NAME to become stable..."
            aws ecs wait services-stable \
              --cluster ${{ env.AWS_CLUSTER }} \
              --services $WORKER_NAME
          fi
          
          echo "All deployments completed successfully!"
